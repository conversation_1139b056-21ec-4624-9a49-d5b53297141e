profiles:
  esp32s3-ospi:
    # If you change this line, change it in build.yml as well
    fqbn: esp32:esp32:esp32s3:CDCOnBoot=cdc,FlashSize=8M,PSRAM=opi,CPUFreq=80,USBMode=hwcdc,FlashMode=qio,PartitionScheme=custom,DebugLevel=none
    platforms:
      - platform: esp32:esp32 (3.2.0)
    libraries:
      - PU2CLR SI4735 (2.1.8)
      - TFT_eSPI (2.5.43)
      - Async TCP (3.4.4)
      - ESP Async WebServer (3.7.8)
      - NTPClient (3.2.1)
      - NimBLE-Arduino (2.3.0)
      - NuS-NimBLE-Serial (4.1.0)

  esp32s3-qspi:
    # If you change this line, change it in build.yml as well
    fqbn: esp32:esp32:esp32s3:CDCOnBoot=cdc,FlashSize=8M,PSRAM=enabled,CPUFreq=80,USBMode=hwcdc,FlashMode=qio,PartitionScheme=custom,DebugLevel=none
    platforms:
      - platform: esp32:esp32 (3.2.0)
    libraries:
      - PU2CLR SI4735 (2.1.8)
      - TFT_eSPI (2.5.43)
      - Async TCP (3.4.4)
      - ESP Async WebServer (3.7.8)
      - NTPClient (3.2.1)
      - NimBLE-Arduino (2.3.0)
      - NuS-NimBLE-Serial (4.1.0)

default_profile: esp32s3-ospi
