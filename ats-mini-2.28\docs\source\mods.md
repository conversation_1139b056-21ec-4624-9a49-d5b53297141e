# Hardware mods

## BOOT and RESET buttons

Some of the ESP32-SI4732 receivers do not have the BOOT and RESET buttons soldered in. You will need these buttons if you want to recover a receiver that was bricked because of a failed flashing process.

You can use a pair of precision tweezers like this to short the button contacts:

![](_static/precision-tweezers.jpg)

Or solder the missing buttons:

![](_static/button.jpg) ![](_static/boot-reset-buttons-missing.jpg) ![](_static/boot-reset-buttons-soldered.jpg)

## Speaker

Some of the units have a smaller speaker. You can install a bigger one (1511) that is used in the original project ([aliexpress.com](https://www.aliexpress.com/item/1005006309723573.html), [aliexpress.ru](https://aliexpress.ru/item/1005005725740853.html)):

![](_static/speaker.jpg)

## Hi-Z match circuit

[ Micro Pocket SSB Receiver - NEW FIRMWARE + Hi-Z CIRCUIT Mod](https://youtu.be/BzrOE9BFpyU?feature=shared&t=916)

## Headphone amplifier

* <https://t.me/talkradio/174172/184530>

Note: a new hardware revision with the LM4809 headphone amplifier has been spotted, see <https://forum.vcfm.ru/viewtopic.php?f=9&t=2395&start=60#p187797>

## 3D-printed cases

* <https://www.printables.com/model/1295639-ats-mini-receiver-housing>

## Other interesting mods

* [Si4732 MiniRX Modifications](https://peterneufeld.wordpress.com/2025/06/13/si4732a-minirx-modifications/) (also see [#73](https://github.com/esp32-si4732/ats-mini/discussions/73))
* [Si4732 Mini & ATS-20+ Hybrid Radio](https://www.youtube.com/watch?v=OyrTHNhbh58)
