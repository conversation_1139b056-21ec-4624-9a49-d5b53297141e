[SECURE BOOT]
secure_boot_en = False
public_key_digest_path = .\secure\public_key_digest.bin
public_key_digest_block_index = 0

[FLASH ENCRYPTION]
flash_encryption_en = False
reserved_burn_times = 0
flash_encrypt_key_block_index = 1

[SECURE OTHER CONFIG]
flash_encryption_use_customer_key_enable = False
flash_encryption_use_customer_key_path = .\secure\flash_encrypt_key.bin
flash_force_write_enable = False

[FLASH ENCRYPTION KEYS LOCAL SAVE]
keys_save_enable = False
encrypt_keys_enable = False
encrypt_keys_aeskey_path = 

[ESP32S3 EFUSE BIT CONFIG]
dis_usb_jtag = False
hard_dis_jtag = False
soft_dis_jtag = 7
dis_usb_otg_download_mode = False
dis_direct_boot = False
dis_download_icache = False
dis_download_dcache = False
dis_download_manual_encrypt = False

