## Contributing guidelines

* This is a learning project.
* People have different usage scenarios, UI preferences, etc. It is not possible to fit everything into a single firmware.
* All feature requests, ideas, observations, questions and answers should go into the [Discussions](https://github.com/esp32-si4732/ats-mini/discussions) section.
* [Issues](https://github.com/esp32-si4732/ats-mini/issues) should be used only for bugs and planned tasks.
* [Pull Requests](https://github.com/esp32-si4732/ats-mini/pulls) are not guaranteed to be accepted, unless the maintainer(s) consider them suitable for the majority of users. Documentation, bugfixes and code quality improvements are usually welcome! If in doubt, please propose your contribution as a [Discussion](https://github.com/esp32-si4732/ats-mini/discussions) first.
* You are encouraged to make your own custom firmware forks! Feel free to share a link to your firmware version in the [Discussions](https://github.com/esp32-si4732/ats-mini/discussions). Interesting features or color themes might be included into the ATS Mini firmware.
