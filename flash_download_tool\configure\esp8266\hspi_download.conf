[EFUSE CHECK]
efuse_mode = 1
efuse_err_halt = 1

[MULTI_UI_CONFIG]
multi_col = 2

[DOWNLOAD PATH]
file_sel0 = 0
file_path0 = 
file_flag0 = False
file_offset0 = 
file_sel1 = 0
file_path1 = 
file_flag1 = False
file_offset1 = 
file_sel2 = 0
file_path2 = 
file_flag2 = False
file_offset2 = 
file_sel3 = 0
file_path3 = 
file_flag3 = False
file_offset3 = 
file_sel4 = 0
file_path4 = 
file_flag4 = False
file_offset4 = 
file_sel5 = 0
file_path5 = 
file_flag5 = False
file_offset5 = 
file_sel6 = 0
file_path6 = 
file_flag6 = False
file_offset6 = 
file_sel7 = 0
file_path7 = 
file_flag7 = False
file_offset7 = 
file_sel8 = 0
file_path8 = 
file_flag8 = False
file_offset8 = 
file_sel9 = 0
file_path9 = 
file_flag9 = False
file_offset9 = 
file_sel10 = 0
file_path10 = 
file_flag10 = False
file_offset10 = 
file_sel11 = 0
file_path11 = 
file_flag11 = False
file_offset11 = 
file_sel12 = 0
file_path12 = 
file_flag12 = False
file_offset12 = 
file_sel13 = 0
file_path13 = 
file_flag13 = False
file_offset13 = 
default_path = C:\Users\<USER>\Documents\Arduino\Projects\ats mini v2\flash_download_tool

[FLASH_CRYSTAL]
spicfgdis = 1
spispeed = 0
spimode = 0

[DOWNLOAD]
erase_button_en = True
autostart1 = 0
com_port1 = 
baudrate1 = 0
checkmac1 = 1

[LOG_CHECK]
log_check_enable = False
log_check_baud = 115200
log_check_str = 1.0.0
log_check_delaytime = 3
log_check_timeout = 3
log_check_cmd_str = AT+GMR
log_check_enable_cmd = False

[MAC_SAVE]
mac_save_enable = False

[ESPTOOL_PARAM]
after = no_reset
before = default_reset
compress = True
flash_size = keep
no_stub = False
verify = True
flash_freq = keep
flash_mode = keep

