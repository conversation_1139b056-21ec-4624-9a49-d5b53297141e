repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-yaml
      - id: end-of-file-fixer
        exclude: |
          (?x)^(
                changelog/.*\.md
          )$
      - id: trailing-whitespace
        args:
          - --markdown-linebreak-ext=md
      - id: mixed-line-ending
        exclude: |
          (?x)^(
                docs/make.bat|
                changelog/.*\.md
          )$
        args:
          - --fix=lf
  - repo: https://github.com/pre-commit/mirrors-clang-format
    rev: v20.1.0
    hooks:
      - id: clang-format
        stages: [manual]
        files: '.*\.[c|cpp|h|ino]'
        args:
          - "--style={BasedOnStyle: llvm, ColumnLimit: 0}"
        exclude: ats-mini/patch_init.h
  - repo: https://github.com/rhysd/actionlint
    rev: v1.7.7
    hooks:
      - id: actionlint
